/**
 * Simple Route Configuration for Authentication
 * Minimal, production-grade solution with existing code structure
 */

// Routes that require authentication (redirect to login if not authenticated)
export const PROTECTED_ROUTES = [
  '/applications',
  '/opportunities',
  '/opportunities/*',
  '/album',
  '/album/*', // Dynamic artist routes

  '/artist',
  '/artist/*', // Dynamic artist routes
  '/playlist',
  '/song',
  '/song/*',
  '/onboarding',
  '/discover',
  '/discover/*', // Nested discover routes
  '/recording'
]

// Routes only for unauthenticated users (redirect to dashboard if authenticated)
export const AUTH_ONLY_ROUTES = [
  '/login',
  '/signup',
  '/forgot-password',
]

// Standalone routes that don't show header/sidebar (but still require auth)
export const STANDALONE_ROUTES = [
  '/remix',
  '/remix/onboarding',
]

// Public routes that don't require authentication and are accessible to all users
export const PUBLIC_ROUTES: string[] = [
  // Currently no public routes - all routes require authentication or are auth-only

]

// All valid routes in the application (for 404 detection)
export const ALL_VALID_ROUTES = [
  ...PROTECTED_ROUTES,
  ...AUTH_ONLY_ROUTES,
  ...STANDALONE_ROUTES,
  ...PUBLIC_ROUTES,
  '/auth/callback', // Auth callback route
  '/opportunities/new', // Specific opportunity routes
  '/opportunities/posted',
]

// Helper to normalize pathnames (remove trailing slash except for root)
function normalizePathname(pathname: string): string {
  if (pathname === '/') return '/';
  return pathname.replace(/\/$/, '');
}

/**
 * Check if a route requires authentication
 */
export function isProtectedRoute(pathname: string): boolean {
  const normalized = normalizePathname(pathname);
  return PROTECTED_ROUTES.some(route => {
    const normalizedRoute = normalizePathname(route);
    // Exact match for single routes
    if (normalizedRoute === normalized) return true;

    // For routes ending with wildcard, check if pathname starts with the base
    if (route.endsWith('/*')) {
      const basePath = route.slice(0, -2);
      return normalized.startsWith(basePath + '/');
    }

    // For other routes, check if pathname starts with route followed by '/' or is exact match
    return normalized === normalizedRoute || normalized.startsWith(normalizedRoute + '/');
  });
}

/**
 * Check if a route is auth-only (for unauthenticated users)
 */
export function isAuthOnlyRoute(pathname: string): boolean {
  const normalized = normalizePathname(pathname);
  return AUTH_ONLY_ROUTES.find(route => normalized === normalizePathname(route)) !== undefined;
}

/**
 * Check if a route is standalone (no header/sidebar)
 */
export function isStandaloneRoute(pathname: string): boolean {
  const normalized = normalizePathname(pathname);
  return STANDALONE_ROUTES.find(route => normalized === normalizePathname(route)) !== undefined;
}

/**
 * Check if a route is public (accessible to all users regardless of auth state)
 */
export function isPublicRoute(pathname: string): boolean {
  const normalized = normalizePathname(pathname);
  return PUBLIC_ROUTES.some(route => normalized === normalizePathname(route));
}

/**
 * Check if a route exists in the application
 */
export function isValidRoute(pathname: string): boolean {
  const normalized = normalizePathname(pathname);

  // Check exact matches first
  const exactMatch = ALL_VALID_ROUTES.some(route => {
    const normalizedRoute = normalizePathname(route);
    return normalizedRoute === normalized;
  });

  if (exactMatch) return true;

  // Check wildcard routes
  return ALL_VALID_ROUTES.some(route => {
    if (route.endsWith('/*')) {
      const basePath = route.slice(0, -2);
      return normalized.startsWith(basePath + '/');
    }
    return false;
  });
}

export function isOnboarding(pathname: string): boolean {
  return pathname.includes("/onboarding");
}

/**
 * Get redirect path based on auth state and current route
 */
export function getAuthRedirect(pathname: string, isAuthenticated: boolean): string | null {
  const normalized = normalizePathname(pathname);

  // First check if the route exists - if not, redirect based on auth state
  if (!isValidRoute(normalized)) {
    // Redirect to appropriate route based on authentication status
    return isAuthenticated ? '/discover' : '/login';
  }

  // Check if route is public (accessible to all)
  if (isPublicRoute(normalized)) {
    return null;
  }

  // Check if route requires authentication (both protected and standalone routes require auth)
  if ((isProtectedRoute(normalized) || isStandaloneRoute(normalized)) && !isAuthenticated) {
    return '/login';
  }

  // Check if authenticated user is trying to access auth-only routes
  if (isAuthenticated && isAuthOnlyRoute(normalized) && !isOnboarding(normalized)) {
    return '/discover';
  }

  return null;
}
