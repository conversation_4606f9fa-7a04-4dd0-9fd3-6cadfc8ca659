'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Play, Download, Send, Settings, Bell } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';


import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import { FileUploadModal } from '@/components/remix/file-upload-modal';
import { UploadFile } from '@/lib/file-upload-utils';
import { useAuth } from '@/contexts/auth/auth-context';
import { useMusicPlayer } from '@/contexts/music-player-context/music-player-context';

function RemixPageContent() {
  const searchParams = useSearchParams()
   const isOnboarded = searchParams.get('onboarding')
  const [isAgreed, setIsAgreed] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(isOnboarded === 'done');
  const [uploadedFiles, setUploadedFiles] = useState<UploadFile[]>([]);
  const router = useRouter();
  const handlePlay = () => console.log('Playing track...');
  const handleDownload = () => console.log('Downloading open verse...');
    const { playSong, currentSong, isPlaying, togglePlay } = useMusicPlayer();
  
const {isAuthenticated} = useAuth()
  const handlePlayClick = () => {
  
    if (currentSong?.id === songDetails.mbid) {
      togglePlay();
    } else {
      playSong({
        id: songDetails.mbid || "",
        title: songDetails.title ?? "",
        artist: (songDetails.creditedOnSong?.[0]?.name || songDetails.creditedOnSong?.[0]?.artistName) ?? "Unknown Artist",
        album: albumsData.length > 0 ? (albumsData[0].title ?? "") : "",
        albumArt: albumCoverPhoto,
        duration: firstRecording?.duration ?? 0,
        audioSrc: audioSrc,
        credits: {} as { producer?: string; writer?: string; engineer?: string },
      });
    }
  };
  const handleSubmit = async () => {

    if (!isAgreed) return;
// if(!isAuthenticated){
// router.push('/login');
// return;
// }
// router.push('/remix/onboarding')
    // Show upload modal instead of redirecting
    setShowUploadModal(true);
  };

  const handleUploadComplete = (files: UploadFile[]) => {
    setUploadedFiles(files);
    console.log('Files uploaded:', files,uploadedFiles);
    // After upload, redirect to onboarding
    router.push('/remix/onboarding');
  };

  const handleCloseModal = () => {
    setShowUploadModal(false);
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Fixed Header */}
      <header className="sticky top-0 z-50 bg-background flex items-center justify-between px-4 md:px-8 lg:px-16 py-4 border-b border-border">
        <div className="flex items-end gap-1">
          <div className="w-28 h-5 relative">
            <Image src="/SMASH-(full)-logo.png" alt="SMASH" fill className="object-contain" />
          </div>
        </div>
        <div className="flex items-center gap-4">
          <button className="w-6 h-6 flex items-center justify-center">
            <Bell className="w-5 h-5 text-foreground" strokeWidth={1.5} />
          </button>
          <button className="w-6 h-6 flex items-center justify-center">
            <Settings className="w-6 h-6 text-foreground" strokeWidth={1.5} />
          </button>
          <Avatar className="w-10 h-10">
            <AvatarImage src="/remix/user-avatar.png" alt="User" />
            <AvatarFallback className="bg-background text-foreground font-medium">U</AvatarFallback>
          </Avatar>
        </div>
      </header>

      {/* Scrollable Main Content */}
      <main className="flex-1 overflow-y-auto bg-background relative">
        {/* Background Geometric Elements */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden" aria-hidden="true">
          {/* Background images - positioned responsively */}
          <div className="hidden lg:block absolute top-48 left-[10%] w-32 h-36 opacity-60">
            <Image src="/remix/bg-image-1-3ba70c.png" alt="" fill className="object-cover" />
          </div>
          <div className="hidden lg:block absolute top-96 left-[15%] w-36 h-40 opacity-60">
            <Image src="/remix/bg-image-3-645d1a.png" alt="" fill className="object-cover" />
          </div>
          <div className="hidden lg:block absolute top-72 right-[10%] w-28 h-32 opacity-60">
            <Image src="/remix/bg-image-2-24c5d3.png" alt="" fill className="object-cover" />
          </div>
          <div className="hidden lg:block absolute top-[32rem] right-[15%] w-32 h-24 opacity-60">
            <Image src="/remix/bg-image-5-786eb3.png" alt="" fill className="object-cover" />
          </div>
          <div className="hidden xl:block absolute bottom-48 right-[5%] w-32 h-24 opacity-60">
            <Image src="/remix/bg-image-4-41e4b7.png" alt="" fill className="object-cover" />
          </div>
        </div>

        {/* Content Container */}
        <div className="relative z-10 max-w-6xl mx-auto px-4 md:px-8 py-8 space-y-6">

          {/* Contest Title Card */}
          <div className="text-center space-y-4">
            <div className="inline-block bg-primary/20 rounded-md px-4 py-1">
              <span className="text-foreground font-semibold text-lg md:text-xl font-lato">Join</span>
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground leading-tight font-arvo">
              Open Verse Contest
            </h2>
            <div className="flex items-center justify-center gap-3">
              <div className="w-9 h-9 rounded-full overflow-hidden">
                <Image src="/remix/kesha-avatar.png" alt="Kesha" width={36} height={36} className="object-cover" />
              </div>
              <span className="text-muted-foreground font-normal text-lg md:text-xl font-arvo">By Kesha</span>
            </div>
          </div>

          {/* Main Hero Image with Pink Circle */}
          <div className="flex justify-center mb-0">
            <div className="relative w-full max-w-md lg:max-w-lg aspect-square">
              <Image
                src="/remix/main-image.png"
                alt="Contest Image"
                fill
                className="object-cover rounded-lg"
              />
              {/* Pink circle overlay - positioned responsively */}
              <div className="absolute top-1/3 left-1/3 w-32 h-32 md:w-36 md:h-36 lg:w-40 lg:h-40 bg-primary rounded-full"></div>
            </div>
          </div>

          {/* Large ATTENTION Title */}
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl lg:text-7xl xl:text-8xl font-bold text-primary leading-tight font-arvo tracking-wide">
              ATTENTION
            </h1>
          </div>

          {/* Play and Download Buttons */}
          <div className="flex flex-col sm:flex-row justify-center items-center gap-4">
            <Button
              onClick={handlePlay}
              className="bg-transparent text-primary border border-primary rounded-full px-6 md:px-8 py-3 font-bold text-sm md:text-base uppercase font-arvo hover:bg-primary/5 flex items-center gap-2"
            >
              <Play className="w-4 h-4 " />
              PLAY
            </Button>
            <Button
              onClick={handleDownload}
              className="bg-transparent text-primary border border-primary rounded-full px-6 md:px-8 py-3 font-bold text-sm md:text-base uppercase font-arvo hover:bg-primary/5 flex items-center gap-2"
            >
              <Download className="w-4 md:w-5 h-4 md:h-5" />
              DOWNLOAD OPEN VERSE
            </Button>
          </div>

          {/* Content Sections */}
          <div className="max-w-4xl mx-auto space-y-8">

            {/* Kesha's Message Section */}
            <section className="bg-card rounded-lg  p-6 md:p-8">
              <h3 className="text-xl md:text-2xl font-bold text-card-foreground font-arvo mb-4">Kesha&apos;s Message</h3>
              <h4 className="text-lg font-bold text-primary mb-4 font-arvo">Do I have your ATTENTION!?</h4>
              <div className="text-card-foreground text-base leading-relaxed font-inter space-y-4">
                <p>It&apos;s official: I&apos;ve created another banger with ATTENTION!, starring me (Kesha), Slayyyter, and Rose Gray. Now it&apos;s time to spread it far and wide and get ATTENTION! all over the world. We&apos;ll be selecting five new versions of ATTENTION!, from five different countries, with five new singers, and pushing it hard.</p>
                <p>And in the spirit of my new startup SMASH, we&apos;re going to make sure that everyone selected gets paid for their hard work.</p>
              </div>
            </section>

            {/* How to Participate Section */}
            <section className="bg-card rounded-lg  p-6 md:p-8">
              <h3 className="text-xl md:text-2xl font-bold text-primary font-arvo uppercase mb-6">HOW TO PARTICIPATE</h3>
              <div className="space-y-6">

                {/* Step 1: Download Open Verse */}
                <div className="flex gap-4">
                  <div className="flex flex-col items-center flex-shrink-0">
                    <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                      <Download className="w-6 h-6 text-primary-foreground" />
                    </div>
                    <div className="w-0.5 h-8 bg-primary mt-2"></div>
                  </div>
                  <div className="flex-1 pt-2">
                    <h4 className="text-lg font-bold text-card-foreground font-arvo mb-2">Download Open Verse</h4>
                    <p className="text-muted-foreground text-base font-lato">Get the official Open Verse recording for &quot;ATTENTION!&quot; here.</p>
                  </div>
                </div>

                {/* Step 2: Create Your Verse */}
                <div className="flex gap-4">
                  <div className="flex flex-col items-center flex-shrink-0">
                    <div className="w-0.5 h-2 bg-primary"></div>
                    <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                      <span className="text-primary-foreground font-bold text-lg">2</span>
                    </div>
                    <div className="w-0.5 h-8 bg-primary mt-2"></div>
                  </div>
                  <div className="flex-1 pt-2">
                    <h4 className="text-lg font-bold text-card-foreground font-arvo mb-2">Create Your Verse</h4>
                    <p className="text-muted-foreground text-base font-lato">Let your creativity flow and record your own lyrics onto the track.</p>
                  </div>
                </div>

                {/* Step 3: Submit Your Recording */}
                <div className="flex gap-4">
                  <div className="flex flex-col items-center flex-shrink-0">
                    <div className="w-0.5 h-2 bg-primary"></div>
                    <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                      <Send className="w-6 h-6 text-primary-foreground" />
                    </div>
                  </div>
                  <div className="flex-1 pt-2">
                    <h4 className="text-lg font-bold text-card-foreground font-arvo mb-2">Submit Your Recording</h4>
                    <p className="text-muted-foreground text-base font-lato">Upload your recording to SMASH and share it with the world.</p>
                  </div>
                </div>
              </div>
            </section>

            {/* Prize Section */}
            <section className="bg-card rounded-lg  p-6 md:p-8">
              <h3 className="text-xl md:text-2xl font-bold text-primary font-arvo uppercase mb-4">PRIZE</h3>
              <p className="text-card-foreground text-base leading-relaxed font-inter">
                The winning remix will drop big-time on SMASH and score a shoutout from Kesha herself — putting your track in the spotlight and giving you major cred from one of music&apos;s biggest icons!
              </p>
            </section>

            {/* Deadline Section */}
            <section className="bg-card rounded-lg  p-6 md:p-8">
              <h3 className="text-xl md:text-2xl font-bold text-primary font-arvo uppercase mb-4">DEADLINE</h3>
              <p className="text-card-foreground text-base leading-relaxed font-inter">
                All remixes must be submitted here by 11:59pm PDT on 4th August 2025 to be considered.
              </p>
            </section>

            {/* Submission Guidelines Section */}
            <section className="bg-card rounded-lg  p-6 md:p-8">
              <h3 className="text-xl md:text-2xl font-bold text-primary font-arvo uppercase mb-4">SUBMISSION GUIDELINES</h3>
              <div className="space-y-4">
                <p className="text-card-foreground text-base leading-relaxed font-inter">
                  By submitting your original work in the correct format and on time, you agree to follow all Smash Music contest rules and allow use of your submission for promotion. Only one entry per participant is allowed. Non-compliance may result in disqualification.
                </p>

                {/* Checkbox */}
                <div className="flex items-start gap-3 pt-2">
                  <Checkbox
                    id="terms-agreement"
                    checked={isAgreed}
                    onCheckedChange={(checked) => setIsAgreed(checked as boolean)}
                    className="mt-1 w-5 h-5 border-primary data-[state=checked]:bg-primary data-[state=checked]:border-primary data-[state=checked]:text-primary-foreground"
                  />
                  <label htmlFor="terms-agreement" className="text-base text-card-foreground cursor-pointer font-semibold font-lato leading-relaxed">
                    I have read and adhere to the contest terms from Smash Music.
                  </label>
                </div>
              </div>
            </section>

            {/* Bottom Action Buttons */}
            <section className="text-center pt-8">
              <div className="flex flex-col sm:flex-row justify-center items-center gap-4">
                <Button
                  onClick={handleDownload}
                  variant="outline"
                  className="bg-transparent text-primary border-primary rounded-full px-6 md:px-8 py-3 font-bold text-sm md:text-base uppercase font-arvo hover:bg-primary/5 flex items-center gap-2"
                >
                  <Download className="w-4 md:w-5 h-4 md:h-5" />
                  DOWNLOAD OPEN VERSE
                </Button>
                <Button
                  onClick={handleSubmit}
                  disabled={!isAgreed}
                  className="bg-primary text-primary-foreground border-primary rounded-full px-6 md:px-8 py-3 font-bold text-sm md:text-base uppercase font-arvo hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                >
                  <Send className="w-4 md:w-5 h-4 md:h-5" />
                  SUBMIT TRACK
                </Button>
              </div>
            </section>
          </div>

          {/* Footer */}
          <footer className="text-center py-8 mt-12">
            <p className="text-foreground text-lg md:text-xl font-semibold font-lato">By Smash Music</p>
          </footer>
        </div>
      </main>

      {/* File Upload Modal */}
      <FileUploadModal
        isOpen={showUploadModal}
        onClose={handleCloseModal}
        onSubmit={handleUploadComplete}
      />
    </div>
  );
}

export default function RemixPage() {
  return <RemixPageContent />;
}
