"use client"

import { useState, useEffect, useRef } from "react"
import Image from "next/image"
import { useMusicPlayer } from "@/contexts/music-player-context/music-player-context"
import { Button } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import {
  Play,
  Pause,
  SkipBack,
  SkipForward,
  Volume2,
  Volume1,
  VolumeX,
  Maximize2,
  Minimize2,
  // ExternalLink, // Commented out as we removed the "View Full Song Page" button
} from "lucide-react"
import { cn } from "@/lib/utils"

export function MusicPlayer() {
  const {
    currentSong,
    isPlaying,
    volume,
    progress,
    duration,
    isExpanded,
    navigationDisabled,
    expandDisabled,
    togglePlay,
    setVolume,
    seekTo,
    nextSong,
    prevSong,
    toggleExpanded,
  } = useMusicPlayer()

  const [showVolumeSlider, setShowVolumeSlider] = useState(false)
  const volumeRef = useRef<HTMLDivElement>(null)

  // Format time in MM:SS
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`
  }

  // Handle click outside volume slider
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (volumeRef.current && !volumeRef.current.contains(event.target as Node)) {
        setShowVolumeSlider(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  // Volume icon based on level
  const VolumeIcon = volume === 0 ? VolumeX : volume < 0.5 ? Volume1 : Volume2

  if (!currentSong) return null

  return (
    <div
      className={cn(
        "w-full bg-background border-t transition-all duration-300",
        isExpanded ? "h-[300px]" : "h-16",
      )}
    >
      {/* Mini Player (Always visible) */}
      <div className="h-16 px-2 sm:px-4 flex items-center justify-between gap-2">
        {/* Mobile: Show only artwork and controls */}
        <div className="flex items-center gap-2 flex-1 sm:hidden">
          {/* <div className="cursor-pointer flex-shrink-0" onClick={toggleExpanded}>
            <div className="w-10 h-10 relative rounded overflow-hidden">
              <Image
                src={currentSong.albumArt || "/placeholder.svg"}
                alt={currentSong.album}
                fill
                className="object-cover"
              />
            </div>
          </div>

          <div className="flex-1 flex items-center justify-center cursor-pointer" onClick={toggleExpanded}>
            <div className="text-center">
              <p className="font-medium text-xs line-clamp-1 truncate">{currentSong.title}</p>
              <p className="text-[10px] text-muted-foreground line-clamp-1 truncate">{currentSong.artist}</p>
            </div>
          </div> */}

          {/* <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 flex-shrink-0"
            onClick={togglePlay}
            aria-label={isPlaying ? "Pause" : "Play"}
          >
            {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
          </Button> */}
        </div>

        {/* Desktop: Show full layout */}
        <div className="hidden sm:flex items-center gap-2 sm:gap-3 min-w-0 flex-shrink">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 sm:h-9 sm:w-9 flex-shrink-0"
            onClick={togglePlay}
            aria-label={isPlaying ? "Pause" : "Play"}
          >
            {isPlaying ? <Pause className="h-4 w-4 sm:h-5 sm:w-5" /> : <Play className="h-4 w-4 sm:h-5 sm:w-5" />}
          </Button>

          <div className="flex items-center gap-2 sm:gap-3 cursor-pointer min-w-0 flex-shrink" onClick={toggleExpanded}>
            {!isExpanded && (
              <div className="w-8 h-8 sm:w-10 sm:h-10 relative rounded overflow-hidden flex-shrink-0">
                <Image
                  src={currentSong.albumArt || "/placeholder.svg"}
                  alt={currentSong.album}
                  fill
                  className="object-cover"
                />
              </div>
            )}
            <div className="min-w-0 flex-shrink">
              <p className="font-medium text-xs sm:text-sm line-clamp-1 truncate">{currentSong.title}</p>
              <p className="text-[10px] sm:text-xs text-muted-foreground line-clamp-1 truncate">{currentSong.artist}</p>
            </div>
          </div>
        </div>

        {/* Desktop progress bar */}
        {!isExpanded && (
          <div className="hidden sm:flex flex-1 items-center mx-2 sm:mx-4">
            <div className="w-full flex items-center gap-1 sm:gap-2">
              <span className="text-xs text-muted-foreground w-8 sm:w-10 text-right text-[10px] sm:text-xs">
                {formatTime(progress)}
              </span>
              <Slider
                value={[progress]}
                max={duration}
                step={1}
                className="flex-1"
                onValueChange={(value) => seekTo(value[0])}
              />
              <span className="text-xs text-muted-foreground w-8 sm:w-10 text-[10px] sm:text-xs">
                {formatTime(duration)}
              </span>
            </div>
          </div>
        )}

        <div className="flex items-center gap-1 sm:gap-2 flex-shrink-0">
          <div className="relative" ref={volumeRef}>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 sm:h-9 sm:w-9"
              onClick={() => setShowVolumeSlider(!showVolumeSlider)}
              aria-label="Volume"
            >
              <VolumeIcon className="h-4 w-4 sm:h-5 sm:w-5" />
            </Button>
            {showVolumeSlider && (
              <div className="absolute bottom-full right-0 mb-2 p-3 bg-background border rounded-md shadow-lg w-32 sm:w-36 z-50">
                <Slider value={[volume]} max={1} step={0.01} onValueChange={(value) => setVolume(value[0])} />
              </div>
            )}
          </div>
          {!expandDisabled && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 sm:h-9 sm:w-9"
              onClick={toggleExpanded}
              aria-label={isExpanded ? "Minimize player" : "Expand player"}
            >
              {isExpanded ? <Minimize2 className="h-4 w-4 sm:h-5 sm:w-5" /> : <Maximize2 className="h-4 w-4 sm:h-5 sm:w-5" />}
            </Button>
          )}
        </div>
      </div>

      {/* Expanded Player */}
      {isExpanded && (
        <div className="p-3 sm:p-4 md:p-6 h-[calc(300px-4rem)] flex flex-col gap-2 sm:gap-3">
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 md:gap-6">
            <div className="flex-shrink-0 self-center sm:self-start">
              <div className="relative w-20 h-20 sm:w-24 sm:h-24 md:w-32 md:h-32 rounded-lg overflow-hidden shadow-lg">
                <Image
                  src={currentSong.albumArt || "/placeholder.svg"}
                  alt={currentSong.album}
                  fill
                  className="object-cover"
                />
              </div>
            </div>

            <div className="flex-1 flex flex-col min-w-0 space-y-2 sm:space-y-3">
              <div className="text-center sm:text-left">
                <h3 className="text-sm sm:text-base md:text-lg font-bold line-clamp-1">{currentSong.title}</h3>
                <p className="text-xs sm:text-sm text-muted-foreground line-clamp-1">{currentSong.artist}</p>
                <p className="text-xs text-muted-foreground line-clamp-1">{currentSong.album}</p>
              </div>

              <div className="flex items-center justify-center gap-2 sm:gap-3">
                {!navigationDisabled && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 sm:h-8 sm:w-8"
                    onClick={prevSong}
                    aria-label="Previous song"
                  >
                    <SkipBack className="h-4 w-4" />
                  </Button>
                )}
                <Button
                  size="icon"
                  className="h-8 w-8 sm:h-10 sm:w-10 rounded-full"
                  onClick={togglePlay}
                  aria-label={isPlaying ? "Pause" : "Play"}
                >
                  {isPlaying ? <Pause className="h-4 w-4 sm:h-5 sm:w-5" /> : <Play className="h-4 w-4 sm:h-5 sm:w-5" />}
                </Button>
                {!navigationDisabled && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 sm:h-8 sm:w-8"
                    onClick={nextSong}
                    aria-label="Next song"
                  >
                    <SkipForward className="h-4 w-4" />
                  </Button>
                )}
              </div>

              {/* <div className="space-y-1">
                <h4 className="text-xs font-medium">Credits:</h4>
                <div className="text-[10px] sm:text-xs text-muted-foreground space-y-0.5">
                  {currentSong.credits?.producer && <p>Producer: {currentSong.credits.producer}</p>}
                  {currentSong.credits?.writer && <p>Writer: {currentSong.credits.writer}</p>}
                  {currentSong.credits?.engineer && <p>Engineer: {currentSong.credits.engineer}</p>}
                </div>
                <p className="text-[10px] sm:text-xs text-muted-foreground">Audio Source: MusicBrainz</p>
              </div> */}
            </div>
          </div>

          {/* Bottom scrubber for expanded player */}
          <div className="flex items-center gap-2 mt-auto pt-2 border-t">
            <span className="text-xs text-muted-foreground w-8 text-right text-[10px] sm:text-xs">
              {formatTime(progress)}
            </span>
            <Slider
              value={[progress]}
              max={duration}
              step={1}
              className="flex-1"
              onValueChange={(value) => seekTo(value[0])}
            />
            <span className="text-xs text-muted-foreground w-8 text-[10px] sm:text-xs">
              {formatTime(duration)}
            </span>
          </div>
        </div>
      )}
    </div>
  )
}
